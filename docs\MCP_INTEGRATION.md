# MCP Integration for CatchUp Customer Service

This document explains the Model Context Protocol (MCP) integration in the CatchUp customer service system.

## Overview

The CatchUp system integrates with an MCP server that provides customer service tools for:
- Category management
- Deal searching and management
- User details retrieval
- Booking management
- Communication tools (email, WhatsApp)

## MCP Server Details

- **Server URL**: `https://genenrativepangea.app.n8n.cloud/mcp/catchup/sse`
- **Transport**: Server-Sent Events (SSE)
- **Tools Available**: 10+ customer service tools

## Architecture

### Components

1. **MCPToolsManager** (`src/shared/mcp_tools.py`)
   - Manages connection to the MCP server
   - Loads and caches tools from the server
   - Handles connection lifecycle

2. **Tools Node** (`src/agent/nodes/tools_node.py`)
   - Executes MCP tools when called by the LLM
   - Integrates with LangGraph's tool execution framework

3. **Enhanced Call Model** (`src/agent/nodes/call_model.py`)
   - Binds MCP tools to the LLM
   - Maintains existing system prompt functionality

4. **Updated Graph** (`src/agent/graph.py`)
   - Includes conditional edges for tool calling
   - Routes between model calls and tool execution

### Flow

1. User sends a message
2. `call_model` node processes the message with bound tools
3. If LLM decides to use tools, flow goes to `tools` node
4. Tools are executed and results returned
5. Flow returns to `call_model` for final response
6. Response is returned to user

## Available Tools

The MCP server provides the following tools (as described in the system prompt):

- `get_categories` - Read all categories
- `search_deals` - Search deals and offers for specific categories
- `get_user_details` - Read information about the user
- `get_chat_history` - Read chat history
- `get_deals` - Get deals and offers from business/company
- `get_business_details` - Read company information
- `get_booking_details` - Get details about existing bookings
- `create_booking` - Create a booking for a deal
- `sent_email_to_users` - Send HTML emails to users
- `whatsapp_sent_tool` - Send WhatsApp messages

## Configuration

### Dependencies

The integration requires the `langchain-mcp-adapters` package:

```toml
dependencies = [
    # ... other dependencies
    "langchain-mcp-adapters>=0.1.9",
]
```

### Environment Variables

No additional environment variables are required for the MCP integration itself, but the underlying LLM may require API keys (e.g., `OPENROUTER_API_KEY`).

## Testing

### Manual Testing

Run the test script to verify the integration:

```bash
python test_mcp_connection.py
```

This will:
1. Test connection to the MCP server
2. Load and display available tools
3. Test the complete graph with tool integration

### Integration Tests

Run the integration tests:

```bash
pytest tests/integration_tests/test_mcp_integration.py -v
```

Note: These tests are skipped by default as they require the MCP server to be available.

## Troubleshooting

### Common Issues

1. **No tools loaded**
   - Check if the MCP server is accessible
   - Verify the server URL is correct
   - Check network connectivity

2. **Tool execution errors**
   - Verify the tool parameters match the expected schema
   - Check if the MCP server is responding correctly

3. **Graph execution issues**
   - Ensure all dependencies are installed
   - Check that the LLM has the necessary API keys

### Debug Mode

To enable debug logging, modify the `MCPToolsManager` to include more verbose logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Development

### Adding New Tools

If new tools are added to the MCP server, they will automatically be available in the system after restarting the application. No code changes are required.

### Modifying Tool Behavior

Tool behavior is controlled by the MCP server. To modify how tools work, update the server implementation, not the client code.

### Custom Tool Processing

If you need custom processing for specific tools, you can modify the `execute_tools` function in `src/agent/nodes/tools_node.py`.

## Security Considerations

- The MCP server should implement proper authentication and authorization
- Tool parameters should be validated on the server side
- Sensitive information should not be logged in tool calls
- Rate limiting should be implemented to prevent abuse

## Performance

- Tools are loaded once at startup and cached
- The MCP connection is reused across multiple tool calls
- Consider implementing connection pooling for high-traffic scenarios
