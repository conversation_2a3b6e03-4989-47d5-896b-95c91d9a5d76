"""LangGraph chatbot graph with checkpointing.

A simple chatbot that uses OpenRouter LLM via llm_factory with conversation persistence.
"""

from __future__ import annotations

import sys
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from typing import Any, Dict, TypedDict
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
from langgraph.checkpoint.memory import MemorySaver

from agent.nodes import call_model
from agent.state import State


class Configuration(TypedDict):
    """Configurable parameters for the chatbot."""
    model_name: str
    system_prompt: str


# Create checkpointer for conversation persistence
checkpointer = MemorySaver()

# Define the graph with checkpointing
graph = (
    StateGraph(State, config_schema=Configuration)
    .add_node("call_model", call_model)
    .add_edge("__start__", "call_model")
    .compile(name="VoiceAgentALL")
)


