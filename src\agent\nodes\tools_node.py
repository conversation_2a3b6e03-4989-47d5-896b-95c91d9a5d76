"""Tools node for executing MCP tools in the CatchUp customer service system."""

from __future__ import annotations

from typing import Any, Dict, List
from langchain_core.messages import ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import ToolNode

from agent.state import State
from shared.mcp_tools import get_catchup_tools


async def create_tools_node() -> ToolNode:
    """Create a tools node with CatchUp MCP tools.
    
    Returns:
        ToolNode configured with CatchUp MCP tools
    """
    tools = await get_catchup_tools()
    return ToolNode(tools)


async def execute_tools(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Execute tools based on tool calls in the last message.
    
    Args:
        state: Current conversation state
        config: Runtime configuration
        
    Returns:
        Updated state with tool execution results
    """
    # Get the tools
    tools = await get_catchup_tools()
    
    # Create tools node
    tools_node = ToolNode(tools)
    
    # Execute the tools node
    result = await tools_node.ainvoke(state, config)
    
    return result
